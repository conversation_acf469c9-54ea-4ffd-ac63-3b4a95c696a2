import re
import random

responses = [
    (r'hi|hello|hey', ["Hello!", "Hi there!", "Hey!"]),
    (r'bye|exit', ["Goodbye!", "See you soon!"]),
    (r'(.*)', ["I didn't understand that. Can you rephrase?"])
]

def chatbot_response(user_input):
    user_input = user_input.lower()
    for pattern, replies in responses:
        if re.search(pattern, user_input):
            return random.choice(replies)
        return "Sorry, I don't understand"